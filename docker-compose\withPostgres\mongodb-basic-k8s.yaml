---
# Basic MongoDB Deployment for Kubernetes (No PVC)
# Uses emptyDir for temporary storage - data will be lost when pod restarts
# Suitable for testing, development, or temporary workloads

# MongoDB Namespace
apiVersion: v1
kind: Namespace
metadata:
  name: mongodb
  labels:
    name: mongodb

---
# MongoDB Secret
apiVersion: v1
kind: Secret
metadata:
  name: mongodb-secret
  namespace: mongodb
type: Opaque
data:
  # Base64 encoded credentials
  # Username: admin, Password: admin123
  # Database: testdb
  mongodb-root-username: YWRtaW4=
  mongodb-root-password: YWRtaW4xMjM=
  mongodb-database: dGVzdGRi

---
# MongoDB Deployment
apiVersion: apps/v1
kind: Deployment
metadata:
  name: mongodb
  namespace: mongodb
  labels:
    app: mongodb
spec:
  replicas: 1
  selector:
    matchLabels:
      app: mongodb
  template:
    metadata:
      labels:
        app: mongodb
    spec:
      containers:
      - name: mongodb
        image: mongo:7.0
        ports:
        - containerPort: 27017
          name: mongodb
        env:
        - name: MONGO_INITDB_ROOT_USERNAME
          valueFrom:
            secretKeyRef:
              name: mongodb-secret
              key: mongodb-root-username
        - name: MONGO_INITDB_ROOT_PASSWORD
          valueFrom:
            secretKeyRef:
              name: mongodb-secret
              key: mongodb-root-password
        - name: MONGO_INITDB_DATABASE
          valueFrom:
            secretKeyRef:
              name: mongodb-secret
              key: mongodb-database
        volumeMounts:
        - name: mongodb-storage
          mountPath: /data/db
        - name: mongodb-logs
          mountPath: /var/log/mongodb
        resources:
          requests:
            memory: "128Mi"
            cpu: "100m"
          limits:
            memory: "256Mi"
            cpu: "200m"
        livenessProbe:
          exec:
            command:
            - mongosh
            - --eval
            - "db.adminCommand('ping')"
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        readinessProbe:
          exec:
            command:
            - mongosh
            - --eval
            - "db.adminCommand('ping')"
          initialDelaySeconds: 5
          periodSeconds: 5
          timeoutSeconds: 3
          failureThreshold: 3
      volumes:
      - name: mongodb-storage
        emptyDir: {}
      - name: mongodb-logs
        emptyDir: {}

---
# MongoDB Service (ClusterIP)
apiVersion: v1
kind: Service
metadata:
  name: mongodb-service
  namespace: mongodb
  labels:
    app: mongodb
spec:
  type: ClusterIP
  selector:
    app: mongodb
  ports:
  - port: 27017
    targetPort: 27017
    name: mongodb

---
# MongoDB LoadBalancer Service (for external access)
apiVersion: v1
kind: Service
metadata:
  name: mongodb-external
  namespace: mongodb
  labels:
    app: mongodb
spec:
  type: LoadBalancer
  selector:
    app: mongodb
  ports:
  - port: 27017
    targetPort: 27017
    name: mongodb

---
# MongoDB ConfigMap (Optional - for custom configuration)
apiVersion: v1
kind: ConfigMap
metadata:
  name: mongodb-config
  namespace: mongodb
data:
  init-script.js: |
    // MongoDB initialization script
    db = db.getSiblingDB('testdb');
    
    // Create a sample collection
    db.createCollection('users');
    
    // Insert sample data
    db.users.insertMany([
      { name: 'John Doe', email: '<EMAIL>', role: 'admin' },
      { name: 'Jane Smith', email: '<EMAIL>', role: 'user' },
      { name: 'Bob Johnson', email: '<EMAIL>', role: 'user' }
    ]);
    
    // Create indexes
    db.users.createIndex({ email: 1 }, { unique: true });
    db.users.createIndex({ role: 1 });
    
    print('Database initialization completed!');

---
# MongoDB Init Job (Optional - to run initialization script)
apiVersion: batch/v1
kind: Job
metadata:
  name: mongodb-init
  namespace: mongodb
spec:
  template:
    spec:
      containers:
      - name: mongodb-init
        image: mongo:7.0
        env:
        - name: MONGO_INITDB_ROOT_USERNAME
          valueFrom:
            secretKeyRef:
              name: mongodb-secret
              key: mongodb-root-username
        - name: MONGO_INITDB_ROOT_PASSWORD
          valueFrom:
            secretKeyRef:
              name: mongodb-secret
              key: mongodb-root-password
        volumeMounts:
        - name: init-script
          mountPath: /docker-entrypoint-initdb.d
        command:
        - /bin/bash
        - -c
        - |
          echo "Waiting for MongoDB to be ready..."
          until mongosh --host mongodb-service.mongodb.svc.cluster.local:27017 --eval "print('MongoDB is ready')" > /dev/null 2>&1; do
            sleep 5
          done
          
          echo "Running initialization script..."
          mongosh --host mongodb-service.mongodb.svc.cluster.local:27017 -u $MONGO_INITDB_ROOT_USERNAME -p $MONGO_INITDB_ROOT_PASSWORD < /docker-entrypoint-initdb.d/init-script.js
          
          echo "MongoDB initialization completed!"
      volumes:
      - name: init-script
        configMap:
          name: mongodb-config
      restartPolicy: OnFailure
  backoffLimit: 3
