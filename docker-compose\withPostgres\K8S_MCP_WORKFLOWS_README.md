# Kubernetes MCP Workflows Documentation

## Overview

I've created two new workflows that use MCP (Model Context Protocol) clients to interact with Kubernetes clusters from different servers:

1. **k8s-mcp-cluster-info-workflow.json** - Basic cluster information retrieval
2. **k8s-mcp-multi-server-workflow.json** - Advanced multi-server operations

## Workflow 1: Basic Cluster Information

### Features
- Get comprehensive cluster overview
- List namespaces, pods, deployments, services
- Resource usage monitoring
- Intelligent query routing

### Usage Examples
```
"Show me cluster overview"
"List all namespaces"
"Get pods in production namespace"
"Show me deployment status"
"What's the resource usage?"
```

### Setup Required
1. Import the workflow into n8n
2. Configure your OpenAI credentials
3. Set up Kubernetes MCP client credentials
4. Test the chat webhook

## Workflow 2: Multi-Server Operations

### Features
- Support for multiple Kubernetes clusters/servers
- Advanced pod operations (logs, describe, top)
- Deployment and service management
- Label-based filtering
- Formatted, user-friendly output

### Supported Operations
- `list_pods` - List pods (with optional namespace/labels)
- `get_pod` - Get specific pod details
- `pod_logs` - Retrieve pod logs
- `list_namespaces` - List all namespaces
- `list_deployments` - List deployments
- `list_services` - List services
- `list_nodes` - List cluster nodes
- `top_pods` - Resource usage for pods
- `describe_pod` - Detailed pod information

### Usage Examples
```
"List pods in usaspenify-stage namespace"
"Show me logs for pod nginx-deployment-abc123"
"Get all deployments with label app=frontend"
"What nodes are in the cluster?"
"Show resource usage for pods in production"
```

## MCP Client Setup

### Prerequisites
1. **MCP Server Running**: You need an MCP server that provides Kubernetes tools
2. **Credentials**: Proper kubeconfig or service account access
3. **Network Access**: n8n must be able to reach your MCP server

### MCP Client Configuration

#### Primary Cluster (existing)
- **Credential ID**: `D1psXmR6kQU6kWRm`
- **Name**: "Kubernetes MCP"
- **Server**: Your primary K8s cluster MCP server

#### Secondary Cluster (new - for multi-server workflow)
- **Credential ID**: `SECONDARY_MCP_CREDENTIAL_ID` (update this)
- **Name**: "Secondary K8s Cluster MCP"
- **Server**: Your secondary K8s cluster MCP server

### Setting Up MCP Credentials in n8n

1. Go to **Settings > Credentials**
2. Click **Add Credential**
3. Select **MCP Client API**
4. Configure:
   ```
   Server URL: http://your-mcp-server:port
   Authentication: Bearer token / API key (if required)
   ```

## Available MCP Tools

The workflows use these Kubernetes MCP tools:

### Core Tools
- `namespaces_list_k8s` - List namespaces
- `pods_list_k8s` - List all pods
- `pods_list_in_namespace_k8s` - List pods in specific namespace
- `pods_get_k8s` - Get specific pod
- `pods_log_k8s` - Get pod logs
- `pods_top_k8s` - Pod resource usage

### Resource Management
- `resources_list_k8s` - List any Kubernetes resource
- `resources_get_k8s` - Get specific resource
- `resources_create_or_update_k8s` - Create/update resources
- `resources_delete_k8s` - Delete resources

### Advanced Operations
- `pods_exec_k8s` - Execute commands in pods
- `pods_run_k8s` - Run new pods
- `pods_delete_k8s` - Delete pods

## Workflow Architecture

### Flow Structure
```
Chat Trigger → AI Agent → Tool Selection → MCP Client → Format Results
```

### Key Components

1. **Chat Trigger**: Webhook endpoint for user interaction
2. **AI Agent**: OpenAI-powered agent that understands requests
3. **Tool Workflow**: Routes requests to appropriate MCP operations
4. **MCP Client**: Executes Kubernetes operations
5. **Formatters**: Present results in user-friendly format

## Deployment Steps

### 1. Import Workflows
```bash
# Copy the JSON files to your n8n workflows directory
# Import via n8n UI: Settings > Import from file
```

### 2. Configure Credentials
- OpenAI API key
- MCP client endpoints
- Kubernetes access (via MCP server)

### 3. Update Credential IDs
In the multi-server workflow, update:
```json
"credentials": {
  "mcpClientApi": {
    "id": "YOUR_SECONDARY_MCP_CREDENTIAL_ID",
    "name": "Your Secondary Cluster Name"
  }
}
```

### 4. Test Connections
- Activate workflows
- Test chat endpoints
- Verify MCP connectivity

## Example Interactions

### Basic Queries
```
User: "What namespaces are available?"
AI: Lists all namespaces with status indicators

User: "Show me pods in production"
AI: Lists pods in production namespace with health status

User: "Get cluster overview"
AI: Comprehensive cluster summary with counts and status
```

### Advanced Queries
```
User: "Show me resource usage for pods with label app=frontend"
AI: Filtered resource usage data

User: "Get logs for the failing pod in staging"
AI: Retrieves and displays pod logs

User: "List all services in the api namespace"
AI: Shows services with types and endpoints
```

## Troubleshooting

### Common Issues

1. **MCP Connection Failed**
   - Check MCP server is running
   - Verify network connectivity
   - Validate credentials

2. **No Results Returned**
   - Check namespace permissions
   - Verify kubeconfig in MCP server
   - Test MCP tools directly

3. **AI Agent Not Understanding**
   - Use more specific language
   - Include namespace/resource names
   - Try alternative phrasings

### Debug Steps

1. Check n8n execution logs
2. Test MCP tools individually
3. Verify Kubernetes permissions
4. Check OpenAI API limits

## Security Considerations

- **Least Privilege**: MCP server should have minimal required permissions
- **Network Security**: Secure MCP server endpoints
- **Credential Management**: Use n8n's secure credential storage
- **Audit Logging**: Enable logging for all operations

## Next Steps

1. Import and configure the workflows
2. Set up your MCP server credentials
3. Test with basic queries
4. Expand to multiple clusters as needed
5. Customize formatting and operations per your requirements

Would you like me to help you configure any specific part of this setup?
