---
# MongoDB Namespace
apiVersion: v1
kind: Namespace
metadata:
  name: mongodb
  labels:
    name: mongodb

---
# MongoDB ConfigMap
apiVersion: v1
kind: ConfigMap
metadata:
  name: mongodb-config
  namespace: mongodb
data:
  mongod.conf: |
    storage:
      dbPath: /data/db
      journal:
        enabled: true
    systemLog:
      destination: file
      logAppend: true
      path: /var/log/mongodb/mongod.log
      logRotate: reopen
    net:
      port: 27017
      bindIp: 0.0.0.0
    processManagement:
      timeZoneInfo: /usr/share/zoneinfo
    replication:
      replSetName: "rs0"
    security:
      authorization: enabled

---
# MongoDB Secret
apiVersion: v1
kind: Secret
metadata:
  name: mongodb-secret
  namespace: mongodb
type: Opaque
data:
  # Default credentials (base64 encoded)
  # Username: admin, Password: password123
  # Change these in production!
  mongodb-root-username: YWRtaW4=
  mongodb-root-password: cGFzc3dvcmQxMjM=
  mongodb-username: bW9uZ29kYnVzZXI=
  mongodb-password: bW9uZ29kYnBhc3M=
  mongodb-database: bW9uZ29kYg==

---
# MongoDB Service (Headless for StatefulSet)
apiVersion: v1
kind: Service
metadata:
  name: mongodb-headless
  namespace: mongodb
  labels:
    app: mongodb
spec:
  clusterIP: None
  selector:
    app: mongodb
  ports:
  - port: 27017
    targetPort: 27017
    name: mongodb

---
# MongoDB Service (ClusterIP for external access)
apiVersion: v1
kind: Service
metadata:
  name: mongodb-service
  namespace: mongodb
  labels:
    app: mongodb
spec:
  type: ClusterIP
  selector:
    app: mongodb
  ports:
  - port: 27017
    targetPort: 27017
    name: mongodb

---
# MongoDB StatefulSet
apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: mongodb
  namespace: mongodb
  labels:
    app: mongodb
spec:
  serviceName: mongodb-headless
  replicas: 3
  selector:
    matchLabels:
      app: mongodb
  template:
    metadata:
      labels:
        app: mongodb
    spec:
      containers:
      - name: mongodb
        image: mongo:7.0
        ports:
        - containerPort: 27017
          name: mongodb
        env:
        - name: MONGO_INITDB_ROOT_USERNAME
          valueFrom:
            secretKeyRef:
              name: mongodb-secret
              key: mongodb-root-username
        - name: MONGO_INITDB_ROOT_PASSWORD
          valueFrom:
            secretKeyRef:
              name: mongodb-secret
              key: mongodb-root-password
        - name: MONGO_INITDB_DATABASE
          valueFrom:
            secretKeyRef:
              name: mongodb-secret
              key: mongodb-database
        volumeMounts:
        - name: mongodb-data
          mountPath: /data/db
        - name: mongodb-config
          mountPath: /etc/mongod.conf
          subPath: mongod.conf
        - name: mongodb-logs
          mountPath: /var/log/mongodb
        command:
        - mongod
        - --config
        - /etc/mongod.conf
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "1Gi"
            cpu: "500m"
        livenessProbe:
          exec:
            command:
            - mongosh
            - --eval
            - "db.adminCommand('ping')"
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        readinessProbe:
          exec:
            command:
            - mongosh
            - --eval
            - "db.adminCommand('ping')"
          initialDelaySeconds: 5
          periodSeconds: 5
          timeoutSeconds: 3
          failureThreshold: 3
      volumes:
      - name: mongodb-config
        configMap:
          name: mongodb-config
      - name: mongodb-logs
        emptyDir: {}
  volumeClaimTemplates:
  - metadata:
      name: mongodb-data
    spec:
      accessModes: ["ReadWriteOnce"]
      storageClassName: "standard"
      resources:
        requests:
          storage: 10Gi

---
# MongoDB PersistentVolume (Optional - for local development)
apiVersion: v1
kind: PersistentVolume
metadata:
  name: mongodb-pv
  labels:
    app: mongodb
spec:
  capacity:
    storage: 10Gi
  accessModes:
  - ReadWriteOnce
  persistentVolumeReclaimPolicy: Retain
  storageClassName: standard
  hostPath:
    path: /data/mongodb

---
# MongoDB Initialization Job
apiVersion: batch/v1
kind: Job
metadata:
  name: mongodb-init
  namespace: mongodb
spec:
  template:
    spec:
      containers:
      - name: mongodb-init
        image: mongo:7.0
        env:
        - name: MONGO_INITDB_ROOT_USERNAME
          valueFrom:
            secretKeyRef:
              name: mongodb-secret
              key: mongodb-root-username
        - name: MONGO_INITDB_ROOT_PASSWORD
          valueFrom:
            secretKeyRef:
              name: mongodb-secret
              key: mongodb-root-password
        command:
        - /bin/bash
        - -c
        - |
          echo "Waiting for MongoDB to be ready..."
          until mongosh --host mongodb-0.mongodb-headless.mongodb.svc.cluster.local:27017 --eval "print('MongoDB is ready')" > /dev/null 2>&1; do
            sleep 5
          done

          echo "Initializing replica set..."
          mongosh --host mongodb-0.mongodb-headless.mongodb.svc.cluster.local:27017 --eval "
            rs.initiate({
              _id: 'rs0',
              members: [
                { _id: 0, host: 'mongodb-0.mongodb-headless.mongodb.svc.cluster.local:27017' },
                { _id: 1, host: 'mongodb-1.mongodb-headless.mongodb.svc.cluster.local:27017' },
                { _id: 2, host: 'mongodb-2.mongodb-headless.mongodb.svc.cluster.local:27017' }
              ]
            })
          "

          echo "MongoDB replica set initialized successfully!"
      restartPolicy: OnFailure
  backoffLimit: 3

---
# MongoDB Monitoring Service (Optional)
apiVersion: v1
kind: Service
metadata:
  name: mongodb-metrics
  namespace: mongodb
  labels:
    app: mongodb
spec:
  type: ClusterIP
  selector:
    app: mongodb
  ports:
  - port: 9216
    targetPort: 9216
    name: metrics
