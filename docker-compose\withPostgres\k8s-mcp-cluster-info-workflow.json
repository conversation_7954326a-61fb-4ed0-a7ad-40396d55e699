{"name": "Kubernetes MCP Cluster Information", "nodes": [{"parameters": {"options": {}}, "type": "@n8n/n8n-nodes-langchain.chatTrigger", "typeVersion": 1.1, "position": [-380, -200], "id": "chat-trigger-k8s", "name": "When chat message received", "webhookId": "k8s-cluster-info-chat"}, {"parameters": {"options": {}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 2, "position": [-68, -200], "id": "ai-agent-k8s", "name": "K8s Cluster AI Agent"}, {"parameters": {"model": {"__rl": true, "mode": "list", "value": "gpt-4o-mini"}, "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "typeVersion": 1.2, "position": [-160, -200], "id": "openai-model-k8s", "name": "OpenAI Chat Model", "credentials": {"openAiApi": {"id": "wVX7NatovCkTa2gs", "name": "OpenAi account"}}}, {"parameters": {}, "type": "@n8n/n8n-nodes-langchain.memoryBufferWindow", "typeVersion": 1.3, "position": [-40, -200], "id": "memory-k8s", "name": "Simple Memory"}, {"parameters": {"operation": "listTools"}, "type": "n8n-nodes-mcp.mcpClientTool", "typeVersion": 1, "position": [80, -200], "id": "mcp-k8s-main", "name": "K8s MCP Client", "credentials": {"mcpClientApi": {"id": "D1psXmR6kQU6kWRm", "name": "Kubernetes MCP "}}}, {"parameters": {"name": "get_cluster_info", "description": "Get comprehensive Kubernetes cluster information including nodes, namespaces, deployments, services, and resource usage", "workflowId": {"__rl": true, "value": "{{ $workflow.id }}", "mode": "id"}, "fields": {"values": [{"name": "info_type", "type": "string", "description": "Type of cluster information to retrieve: 'overview', 'nodes', 'namespaces', 'deployments', 'services', 'pods', 'resources', 'all'"}, {"name": "namespace", "type": "string", "description": "Specific namespace to focus on (optional, for namespace-specific queries)"}]}}, "type": "@n8n/n8n-nodes-langchain.toolWorkflow", "typeVersion": 2.2, "position": [200, -200], "id": "cluster-info-tool", "name": "Cluster Info Tool"}, {"parameters": {"jsCode": "// Determine which MCP tools to call based on the request\nconst inputData = $input.all();\nconst infoType = inputData[0]?.json?.info_type || 'overview';\nconst namespace = inputData[0]?.json?.namespace;\n\nlet toolCalls = [];\n\nswitch(infoType.toLowerCase()) {\n  case 'overview':\n  case 'all':\n    toolCalls = [\n      { tool: 'namespaces_list_k8s', params: {} },\n      { tool: 'pods_list_k8s', params: {} },\n      { tool: 'resources_list_k8s', params: { apiVersion: 'apps/v1', kind: 'Deployment' } },\n      { tool: 'resources_list_k8s', params: { apiVersion: 'v1', kind: 'Service' } }\n    ];\n    break;\n  case 'nodes':\n    toolCalls = [{ tool: 'resources_list_k8s', params: { apiVersion: 'v1', kind: 'Node' } }];\n    break;\n  case 'namespaces':\n    toolCalls = [{ tool: 'namespaces_list_k8s', params: {} }];\n    break;\n  case 'deployments':\n    toolCalls = [{ tool: 'resources_list_k8s', params: { apiVersion: 'apps/v1', kind: 'Deployment', namespace: namespace } }];\n    break;\n  case 'services':\n    toolCalls = [{ tool: 'resources_list_k8s', params: { apiVersion: 'v1', kind: 'Service', namespace: namespace } }];\n    break;\n  case 'pods':\n    if (namespace) {\n      toolCalls = [{ tool: 'pods_list_in_namespace_k8s', params: { namespace: namespace } }];\n    } else {\n      toolCalls = [{ tool: 'pods_list_k8s', params: {} }];\n    }\n    break;\n  case 'resources':\n    toolCalls = [{ tool: 'pods_top_k8s', params: { all_namespaces: true } }];\n    break;\n  default:\n    toolCalls = [{ tool: 'namespaces_list_k8s', params: {} }];\n}\n\nreturn toolCalls.map((call, index) => ({ \n  json: { \n    toolName: call.tool, \n    toolParams: call.params,\n    requestType: infoType,\n    callIndex: index,\n    totalCalls: toolCalls.length\n  } \n}));"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [360, -200], "id": "prepare-mcp-calls", "name": "Prepare MCP Calls"}, {"parameters": {"operation": "executeTool", "toolName": "={{ $json.toolName }}", "toolParameters": "={{ $json.toolParams }}"}, "type": "n8n-nodes-mcp.mcpClientTool", "typeVersion": 1, "position": [520, -200], "id": "execute-mcp-tool", "name": "Execute MCP Tool", "credentials": {"mcpClientApi": {"id": "D1psXmR6kQU6kWRm", "name": "Kubernetes MCP "}}}, {"parameters": {"jsCode": "// Aggregate and format the results from multiple MCP calls\nconst inputData = $input.all();\n\nlet aggregatedResults = {\n  clusterInfo: {},\n  summary: {},\n  details: []\n};\n\n// Process each result\ninputData.forEach(item => {\n  const toolName = item.json.toolName;\n  const result = item.json.result || item.json;\n  const requestType = item.json.requestType;\n  \n  // Store the raw result\n  aggregatedResults.details.push({\n    tool: toolName,\n    data: result\n  });\n  \n  // Create summary based on tool type\n  if (toolName === 'namespaces_list_k8s') {\n    aggregatedResults.clusterInfo.namespaces = result;\n    aggregatedResults.summary.namespaceCount = Array.isArray(result) ? result.length : 0;\n  } else if (toolName === 'pods_list_k8s' || toolName === 'pods_list_in_namespace_k8s') {\n    aggregatedResults.clusterInfo.pods = result;\n    aggregatedResults.summary.podCount = Array.isArray(result) ? result.length : 0;\n  } else if (toolName === 'resources_list_k8s') {\n    if (result && Array.isArray(result)) {\n      if (result[0] && result[0].kind === 'Deployment') {\n        aggregatedResults.clusterInfo.deployments = result;\n        aggregatedResults.summary.deploymentCount = result.length;\n      } else if (result[0] && result[0].kind === 'Service') {\n        aggregatedResults.clusterInfo.services = result;\n        aggregatedResults.summary.serviceCount = result.length;\n      } else if (result[0] && result[0].kind === 'Node') {\n        aggregatedResults.clusterInfo.nodes = result;\n        aggregatedResults.summary.nodeCount = result.length;\n      }\n    }\n  } else if (toolName === 'pods_top_k8s') {\n    aggregatedResults.clusterInfo.resourceUsage = result;\n  }\n});\n\n// Create a formatted summary\naggregatedResults.formattedSummary = `\n🏗️ **Kubernetes Cluster Overview**\n\n📊 **Summary:**\n${aggregatedResults.summary.nodeCount ? `• Nodes: ${aggregatedResults.summary.nodeCount}` : ''}\n${aggregatedResults.summary.namespaceCount ? `• Namespaces: ${aggregatedResults.summary.namespaceCount}` : ''}\n${aggregatedResults.summary.deploymentCount ? `• Deployments: ${aggregatedResults.summary.deploymentCount}` : ''}\n${aggregatedResults.summary.serviceCount ? `• Services: ${aggregatedResults.summary.serviceCount}` : ''}\n${aggregatedResults.summary.podCount ? `• Pods: ${aggregatedResults.summary.podCount}` : ''}\n\n📋 **Detailed Information Available:**\n• Use specific queries for detailed views\n• Example: \"Show me pods in namespace X\"\n• Example: \"Get deployment details\"\n`;\n\nreturn [{ json: aggregatedResults }];"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [680, -200], "id": "format-results", "name": "Format Results"}], "connections": {"When chat message received": {"main": [[{"node": "K8s Cluster AI Agent", "type": "main", "index": 0}]]}, "OpenAI Chat Model": {"ai_languageModel": [[{"node": "K8s Cluster AI Agent", "type": "ai_languageModel", "index": 0}]]}, "Simple Memory": {"ai_memory": [[{"node": "K8s Cluster AI Agent", "type": "ai_memory", "index": 0}]]}, "K8s MCP Client": {"ai_tool": [[{"node": "K8s Cluster AI Agent", "type": "ai_tool", "index": 0}]]}, "Cluster Info Tool": {"ai_tool": [[{"node": "K8s Cluster AI Agent", "type": "ai_tool", "index": 0}]], "main": [[{"node": "Prepare MCP Calls", "type": "main", "index": 0}]]}, "Prepare MCP Calls": {"main": [[{"node": "Execute MCP Tool", "type": "main", "index": 0}]]}, "Execute MCP Tool": {"main": [[{"node": "Format Results", "type": "main", "index": 0}]]}}, "pinData": {}, "meta": {"templateCredsSetupCompleted": true}}