{"nodes": [{"parameters": {"jsCode": "// Format the kubectl command with proper user switching\nconst inputData = $input.all();\nconst command = inputData[0]?.json?.command || 'kubectl get pods -n usaspenify-stage';\nconst namespace = inputData[0]?.json?.namespace || 'usaspenify-stage';\n\n// Build the full command with user switching\nlet fullCommand;\nif (command.includes('kubectl')) {\n  fullCommand = `echo \"\" | sudo -S -u stage -H bash -c \"${command}\"`;\n} else {\n  // If it's a kubectl command without the kubectl prefix, add it\n  fullCommand = `echo \"\" | sudo -S -u stage -H bash -c \"kubectl ${command} -n ${namespace}\"`;\n}\n\nreturn [{ json: { command: fullCommand, originalCommand: command, namespace: namespace } }];"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [360, -220], "id": "prep-ssh-command", "name": "Prepare SSH Command"}, {"parameters": {"command": "={{ $json.command }}", "options": {}}, "type": "n8n-nodes-base.ssh", "typeVersion": 1, "position": [500, -220], "id": "9ed64801-5601-4318-8ea0-e5dee7c35e46", "name": "SSH Execute Command", "credentials": {"sshPassword": {"id": "C7yk4axsxv8FPMsc", "name": "Aspenify_Automation_SSH_Password"}}}, {"parameters": {"inputSource": "passthrough"}, "type": "n8n-nodes-base.executeWorkflowTrigger", "typeVersion": 1.1, "position": [-140, 40], "id": "50179a30-3442-429d-b1e2-e01d0f96540e", "name": "When Executed by Another Workflow"}, {"parameters": {}, "type": "n8n-nodes-mcp.mcpClientTool", "typeVersion": 1, "position": [80, -220], "id": "a377ae0f-a9b8-4e33-8df0-82f32b7b03ec", "name": "K8s MCP Client2", "credentials": {"mcpClientApi": {"id": "D1psXmR6kQU6kWRm", "name": "Kubernetes MCP "}}}, {"parameters": {}, "type": "@n8n/n8n-nodes-langchain.memoryBufferWindow", "typeVersion": 1.3, "position": [-40, -220], "id": "38f27221-9bee-4533-9ce3-e8c4d3555042", "name": "Simple Memory"}, {"parameters": {"model": {"__rl": true, "mode": "list", "value": "gpt-4o-mini"}, "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "typeVersion": 1.2, "position": [-160, -220], "id": "28f89bb8-659c-455e-9f8a-6f8874d1e2d5", "name": "OpenAI Chat Model", "credentials": {"openAiApi": {"id": "wVX7NatovCkTa2gs", "name": "OpenAi account"}}}, {"parameters": {"options": {}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 2, "position": [-68, -440], "id": "d85384b1-f2f7-4349-a16a-bf25eef71d88", "name": "AI Agent"}, {"parameters": {"options": {}}, "type": "@n8n/n8n-nodes-langchain.chatTrigger", "typeVersion": 1.1, "position": [-380, -440], "id": "799afaba-d021-4ec9-b6e8-a7ef265c0fe5", "name": "When chat message received", "webhookId": "4fe7256b-f5d5-46a8-8aae-be46cd4a08e7"}, {"parameters": {"name": "ssh_kubectl", "description": "Execute kubectl commands on the server by SSH. Use this tool to get pods, services, deployments, etc. from Kubernetes clusters.", "workflowId": {"__rl": true, "value": "{{ $workflow.id }}", "mode": "id"}, "fields": {"values": [{"name": "command", "type": "string", "description": "The kubectl command to execute (e.g., 'kubectl get pods -n namespace')"}, {"name": "namespace", "type": "string", "description": "Kubernetes namespace (optional, defaults to usaspenify-stage)"}]}}, "type": "@n8n/n8n-nodes-langchain.toolWorkflow", "typeVersion": 2.2, "position": [200, -220], "id": "4049dc28-2cd0-4d4b-87e2-231d0587beab", "name": "SSH Kubectl Tool"}, {"parameters": {"operation": "executeTool", "toolName": "=", "toolParameters": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Tool_Parameters', ``, 'json') }}"}, "type": "n8n-nodes-mcp.mcpClientTool", "typeVersion": 1, "position": [340, -220], "id": "4906a90f-9aa4-4e19-9f7d-8526871ed108", "name": "MCP Client", "credentials": {"mcpClientApi": {"id": "D1psXmR6kQU6kWRm", "name": "Kubernetes MCP "}}}], "connections": {"Aspenify_SSH_Execute a command": {"main": [[]]}, "When Executed by Another Workflow": {"main": [[{"node": "Prepare SSH Command", "type": "main", "index": 0}]]}, "K8s MCP Client2": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "Simple Memory": {"ai_memory": [[{"node": "AI Agent", "type": "ai_memory", "index": 0}]]}, "OpenAI Chat Model": {"ai_languageModel": [[{"node": "AI Agent", "type": "ai_languageModel", "index": 0}]]}, "When chat message received": {"main": [[{"node": "AI Agent", "type": "main", "index": 0}]]}, "SSH Kubectl Tool": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]], "main": [[{"node": "Prepare SSH Command", "type": "main", "index": 0}]]}, "Prepare SSH Command": {"main": [[{"node": "SSH Execute Command", "type": "main", "index": 0}]]}, "MCP Client": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}}, "pinData": {}, "meta": {"templateCredsSetupCompleted": true, "instanceId": "6adb50ce813b974c935afa2ae535a24b21e03cb58fef5afdc87f7eaef22a20b3"}}