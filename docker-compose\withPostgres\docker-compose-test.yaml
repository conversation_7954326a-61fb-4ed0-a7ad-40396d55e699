version: '3.8'

volumes:
  db_storages:
  n8n_storages:
  caddy_datas:
  caddy_configs:

services:
  postgres:
    image: postgres:16
    restart: always
    env_file:
      - .env
    volumes:
      - db_storages:/var/lib/postgresql/data
      - ./init-data.sh:/docker-entrypoint-initdb.d/init-data.sh
    healthcheck:
      test: ['CMD-SHELL', 'pg_isready -U ${POSTGRES_USER} -d ${POSTGRES_DB}']
      interval: 5s
      timeout: 5s
      retries: 10

  n8n:
    image: docker.n8n.io/n8nio/n8n
    restart: always
    env_file:
      - .env
    environment:
      - DB_TYPE=postgres
      - DB_POSTGRESDB_HOST=postgres
      - DB_POSTGRESDB_PORT=5432
      - DB_POSTGRESDB_DATABASE=${POSTGRES_DB}
      - DB_POSTGRESDB_USER=${POSTGRES_NON_ROOT_USER}
      - DB_POSTGRESDB_PASSWORD=${POSTGRES_NON_ROOT_PASSWORD}
      - N8N_HOST=n8n.example.com
      - N8N_PORT=5678
      - WEBHOOK_URL=http://n8n.example.com
      - N8N_PROTOCOL=https
      - N8N_COMMUNITY_PACKAGES_ALLOW_TOOL_USAGE=true
      - N8N_COMMUNITY_PACKAGES_ENABLED=true
      - N8N_REINSTALL_MISSING_PACKAGES=true
      - N8N_DISABLE_PRODUCTION_MAIN_PROCESS_RESPONSE_COMPRESSION=true
      - GITLAB_TOKEN=********************
      - N8N_SECURE_COOKIE=false
    volumes:
      - n8n_storages:/home/<USER>/.n8n
    expose:
      - 5678
    depends_on:
      postgres:
        condition: service_healthy

  caddy:
    image: caddy:latest
    restart: always
    ports:
      - "80:80"
    volumes:
      - ./Caddyfile:/etc/caddy/Caddyfile
      - caddy_datas:/data
      - caddy_configs:/config
