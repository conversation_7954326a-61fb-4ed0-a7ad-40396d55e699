{"name": "Kubernetes MCP Multi-Server Management", "nodes": [{"parameters": {"options": {}}, "type": "@n8n/n8n-nodes-langchain.chatTrigger", "typeVersion": 1.1, "position": [-400, 0], "id": "multi-server-chat-trigger", "name": "Multi-Server <PERSON><PERSON>", "webhookId": "k8s-multi-server-chat"}, {"parameters": {"options": {}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 2, "position": [-100, 0], "id": "multi-server-ai-agent", "name": "Multi-Server K8s AI Agent"}, {"parameters": {"model": {"__rl": true, "mode": "list", "value": "gpt-4o-mini"}, "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "typeVersion": 1.2, "position": [-200, 0], "id": "multi-server-openai", "name": "OpenAI Chat Model", "credentials": {"openAiApi": {"id": "wVX7NatovCkTa2gs", "name": "OpenAi account"}}}, {"parameters": {}, "type": "@n8n/n8n-nodes-langchain.memoryBufferWindow", "typeVersion": 1.3, "position": [-50, 0], "id": "multi-server-memory", "name": "Conversation Memory"}, {"parameters": {"name": "k8s_cluster_operations", "description": "Perform Kubernetes operations on different clusters/servers. Supports: cluster info, pod management, deployment operations, service discovery, namespace operations, resource monitoring", "workflowId": {"__rl": true, "value": "{{ $workflow.id }}", "mode": "id"}, "fields": {"values": [{"name": "operation", "type": "string", "description": "Operation type: 'list_pods', 'get_pod', 'list_namespaces', 'list_deployments', 'list_services', 'get_cluster_info', 'pod_logs', 'describe_pod', 'top_pods', 'list_nodes'"}, {"name": "namespace", "type": "string", "description": "Kubernetes namespace (optional for cluster-wide operations)"}, {"name": "resource_name", "type": "string", "description": "Name of specific resource (pod, deployment, service, etc.)"}, {"name": "label_selector", "type": "string", "description": "Label selector for filtering resources (e.g., 'app=myapp,env=prod')"}]}}, "type": "@n8n/n8n-nodes-langchain.toolWorkflow", "typeVersion": 2.2, "position": [100, 0], "id": "k8s-operations-tool", "name": "K8s Operations Tool"}, {"parameters": {"jsCode": "// Route the operation to the appropriate MCP tool\nconst inputData = $input.all();\nconst operation = inputData[0]?.json?.operation || 'get_cluster_info';\nconst namespace = inputData[0]?.json?.namespace;\nconst resourceName = inputData[0]?.json?.resource_name;\nconst labelSelector = inputData[0]?.json?.label_selector;\n\nlet mcpToolCall = {\n  toolName: '',\n  toolParams: {},\n  operation: operation\n};\n\nswitch(operation.toLowerCase()) {\n  case 'list_pods':\n    if (namespace) {\n      mcpToolCall.toolName = 'pods_list_in_namespace_k8s';\n      mcpToolCall.toolParams = { namespace: namespace };\n    } else {\n      mcpToolCall.toolName = 'pods_list_k8s';\n      mcpToolCall.toolParams = {};\n    }\n    if (labelSelector) {\n      mcpToolCall.toolParams.labelSelector = labelSelector;\n    }\n    break;\n    \n  case 'get_pod':\n    mcpToolCall.toolName = 'pods_get_k8s';\n    mcpToolCall.toolParams = { name: resourceName };\n    if (namespace) mcpToolCall.toolParams.namespace = namespace;\n    break;\n    \n  case 'pod_logs':\n    mcpToolCall.toolName = 'pods_log_k8s';\n    mcpToolCall.toolParams = { name: resourceName };\n    if (namespace) mcpToolCall.toolParams.namespace = namespace;\n    break;\n    \n  case 'list_namespaces':\n    mcpToolCall.toolName = 'namespaces_list_k8s';\n    mcpToolCall.toolParams = {};\n    break;\n    \n  case 'list_deployments':\n    mcpToolCall.toolName = 'resources_list_k8s';\n    mcpToolCall.toolParams = { apiVersion: 'apps/v1', kind: 'Deployment' };\n    if (namespace) mcpToolCall.toolParams.namespace = namespace;\n    if (labelSelector) mcpToolCall.toolParams.labelSelector = labelSelector;\n    break;\n    \n  case 'list_services':\n    mcpToolCall.toolName = 'resources_list_k8s';\n    mcpToolCall.toolParams = { apiVersion: 'v1', kind: 'Service' };\n    if (namespace) mcpToolCall.toolParams.namespace = namespace;\n    if (labelSelector) mcpToolCall.toolParams.labelSelector = labelSelector;\n    break;\n    \n  case 'list_nodes':\n    mcpToolCall.toolName = 'resources_list_k8s';\n    mcpToolCall.toolParams = { apiVersion: 'v1', kind: 'Node' };\n    break;\n    \n  case 'top_pods':\n    mcpToolCall.toolName = 'pods_top_k8s';\n    mcpToolCall.toolParams = { all_namespaces: !namespace };\n    if (namespace) mcpToolCall.toolParams.namespace = namespace;\n    if (resourceName) mcpToolCall.toolParams.name = resourceName;\n    break;\n    \n  case 'describe_pod':\n    mcpToolCall.toolName = 'pods_get_k8s';\n    mcpToolCall.toolParams = { name: resourceName };\n    if (namespace) mcpToolCall.toolParams.namespace = namespace;\n    break;\n    \n  case 'get_cluster_info':\n  default:\n    // For cluster info, we'll make multiple calls\n    mcpToolCall.toolName = 'namespaces_list_k8s';\n    mcpToolCall.toolParams = {};\n    mcpToolCall.multiCall = true;\n    break;\n}\n\nreturn [{ json: mcpToolCall }];"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [280, 0], "id": "route-operation", "name": "Route Operation"}, {"parameters": {"operation": "executeTool", "toolName": "={{ $json.toolName }}", "toolParameters": "={{ $json.toolParams }}"}, "type": "n8n-nodes-mcp.mcpClientTool", "typeVersion": 1, "position": [460, 0], "id": "primary-mcp-client", "name": "Primary MCP Client", "credentials": {"mcpClientApi": {"id": "D1psXmR6kQU6kWRm", "name": "Kubernetes MCP "}}}, {"parameters": {"operation": "executeTool", "toolName": "={{ $json.toolName }}", "toolParameters": "={{ $json.toolParams }}"}, "type": "n8n-nodes-mcp.mcpClientTool", "typeVersion": 1, "position": [460, 150], "id": "secondary-mcp-client", "name": "Secondary MCP Client", "credentials": {"mcpClientApi": {"id": "SECONDARY_MCP_CREDENTIAL_ID", "name": "Secondary K8s Cluster MCP"}}}, {"parameters": {"jsCode": "// Format and present the results in a user-friendly way\nconst inputData = $input.all();\nconst operation = inputData[0]?.json?.operation;\nconst result = inputData[0]?.json?.result || inputData[0]?.json;\n\nlet formattedOutput = {\n  operation: operation,\n  rawResult: result,\n  summary: '',\n  formattedResult: ''\n};\n\nswitch(operation) {\n  case 'list_pods':\n    if (Array.isArray(result)) {\n      formattedOutput.summary = `Found ${result.length} pods`;\n      formattedOutput.formattedResult = `🚀 **Pods List (${result.length} total)**\\n\\n` +\n        result.map(pod => {\n          const status = pod.status?.phase || 'Unknown';\n          const namespace = pod.metadata?.namespace || 'default';\n          const name = pod.metadata?.name || 'unnamed';\n          const ready = pod.status?.containerStatuses?.every(c => c.ready) ? '✅' : '❌';\n          return `${ready} **${name}** (${namespace}) - ${status}`;\n        }).join('\\n');\n    }\n    break;\n    \n  case 'list_namespaces':\n    if (Array.isArray(result)) {\n      formattedOutput.summary = `Found ${result.length} namespaces`;\n      formattedOutput.formattedResult = `📁 **Namespaces (${result.length} total)**\\n\\n` +\n        result.map(ns => {\n          const name = ns.metadata?.name || 'unnamed';\n          const status = ns.status?.phase || 'Unknown';\n          const active = status === 'Active' ? '✅' : '❌';\n          return `${active} **${name}** - ${status}`;\n        }).join('\\n');\n    }\n    break;\n    \n  case 'list_deployments':\n    if (Array.isArray(result)) {\n      formattedOutput.summary = `Found ${result.length} deployments`;\n      formattedOutput.formattedResult = `🚀 **Deployments (${result.length} total)**\\n\\n` +\n        result.map(dep => {\n          const name = dep.metadata?.name || 'unnamed';\n          const namespace = dep.metadata?.namespace || 'default';\n          const replicas = dep.status?.replicas || 0;\n          const ready = dep.status?.readyReplicas || 0;\n          const available = ready === replicas ? '✅' : '⚠️';\n          return `${available} **${name}** (${namespace}) - ${ready}/${replicas} ready`;\n        }).join('\\n');\n    }\n    break;\n    \n  case 'list_services':\n    if (Array.isArray(result)) {\n      formattedOutput.summary = `Found ${result.length} services`;\n      formattedOutput.formattedResult = `🌐 **Services (${result.length} total)**\\n\\n` +\n        result.map(svc => {\n          const name = svc.metadata?.name || 'unnamed';\n          const namespace = svc.metadata?.namespace || 'default';\n          const type = svc.spec?.type || 'ClusterIP';\n          const clusterIP = svc.spec?.clusterIP || 'None';\n          return `🔗 **${name}** (${namespace}) - ${type} - ${clusterIP}`;\n        }).join('\\n');\n    }\n    break;\n    \n  case 'top_pods':\n    if (Array.isArray(result)) {\n      formattedOutput.summary = `Resource usage for ${result.length} pods`;\n      formattedOutput.formattedResult = `📊 **Pod Resource Usage**\\n\\n` +\n        result.map(pod => {\n          const name = pod.metadata?.name || 'unnamed';\n          const namespace = pod.metadata?.namespace || 'default';\n          const cpu = pod.usage?.cpu || 'N/A';\n          const memory = pod.usage?.memory || 'N/A';\n          return `📈 **${name}** (${namespace}) - CPU: ${cpu}, Memory: ${memory}`;\n        }).join('\\n');\n    }\n    break;\n    \n  case 'pod_logs':\n    formattedOutput.summary = 'Pod logs retrieved';\n    formattedOutput.formattedResult = `📋 **Pod Logs**\\n\\n\\`\\`\\`\\n${result}\\n\\`\\`\\``;\n    break;\n    \n  default:\n    formattedOutput.summary = 'Operation completed';\n    formattedOutput.formattedResult = `📋 **Result**\\n\\n${JSON.stringify(result, null, 2)}`;\n}\n\nreturn [{ json: formattedOutput }];"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [640, 0], "id": "format-output", "name": "Format Output"}], "connections": {"Multi-Server Chat Trigger": {"main": [[{"node": "Multi-Server K8s AI Agent", "type": "main", "index": 0}]]}, "OpenAI Chat Model": {"ai_languageModel": [[{"node": "Multi-Server K8s AI Agent", "type": "ai_languageModel", "index": 0}]]}, "Conversation Memory": {"ai_memory": [[{"node": "Multi-Server K8s AI Agent", "type": "ai_memory", "index": 0}]]}, "K8s Operations Tool": {"ai_tool": [[{"node": "Multi-Server K8s AI Agent", "type": "ai_tool", "index": 0}]], "main": [[{"node": "Route Operation", "type": "main", "index": 0}]]}, "Route Operation": {"main": [[{"node": "Primary MCP Client", "type": "main", "index": 0}]]}, "Primary MCP Client": {"main": [[{"node": "Format Output", "type": "main", "index": 0}]]}, "Secondary MCP Client": {"main": [[{"node": "Format Output", "type": "main", "index": 0}]]}}, "pinData": {}, "meta": {"templateCredsSetupCompleted": false}}