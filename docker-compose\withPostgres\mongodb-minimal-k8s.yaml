---
# Minimal MongoDB Deployment for Kubernetes
# No PVC, no initialization - just basic MongoDB
# Perfect for quick testing and development

# MongoDB Namespace
apiVersion: v1
kind: Namespace
metadata:
  name: mongodb

---
# MongoDB Secret
apiVersion: v1
kind: Secret
metadata:
  name: mongodb-secret
  namespace: mongodb
type: Opaque
data:
  # Base64 encoded: admin / admin123
  mongodb-root-username: YWRtaW4=
  mongodb-root-password: YWRtaW4xMjM=

---
# MongoDB Deployment
apiVersion: apps/v1
kind: Deployment
metadata:
  name: mongodb
  namespace: mongodb
  labels:
    app: mongodb
spec:
  replicas: 1
  selector:
    matchLabels:
      app: mongodb
  template:
    metadata:
      labels:
        app: mongodb
    spec:
      containers:
      - name: mongodb
        image: mongo:7.0
        ports:
        - containerPort: 27017
        env:
        - name: MONGO_INITDB_ROOT_USERNAME
          valueFrom:
            secretKeyRef:
              name: mongodb-secret
              key: mongodb-root-username
        - name: MONGO_INITDB_ROOT_PASSWORD
          valueFrom:
            secretKeyRef:
              name: mongodb-secret
              key: mongodb-root-password
        volumeMounts:
        - name: mongodb-data
          mountPath: /data/db
        resources:
          requests:
            memory: "128Mi"
            cpu: "100m"
          limits:
            memory: "256Mi"
            cpu: "200m"
      volumes:
      - name: mongodb-data
        emptyDir: {}

---
# MongoDB Service
apiVersion: v1
kind: Service
metadata:
  name: mongodb
  namespace: mongodb
  labels:
    app: mongodb
spec:
  selector:
    app: mongodb
  ports:
  - port: 27017
    targetPort: 27017

---
# MongoDB NodePort Service (for external access)
apiVersion: v1
kind: Service
metadata:
  name: mongodb-nodeport
  namespace: mongodb
  labels:
    app: mongodb
spec:
  type: NodePort
  selector:
    app: mongodb
  ports:
  - port: 27017
    targetPort: 27017
    nodePort: 30017
