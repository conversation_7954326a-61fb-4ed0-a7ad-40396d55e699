---
# Simple MongoDB Deployment for Kubernetes
# This is a single-instance deployment suitable for development/testing

# MongoDB Namespace
apiVersion: v1
kind: Namespace
metadata:
  name: mongodb
  labels:
    name: mongodb

---
# MongoDB Secret
apiVersion: v1
kind: Secret
metadata:
  name: mongodb-secret
  namespace: mongodb
type: Opaque
data:
  # Base64 encoded credentials
  # Username: admin, Password: mongopass123
  # Database: myapp
  mongodb-root-username: YWRtaW4=
  mongodb-root-password: bW9uZ29wYXNzMTIz
  mongodb-username: YXBwdXNlcg==
  mongodb-password: YXBwcGFzcw==
  mongodb-database: bXlhcHA=

---
# MongoDB PersistentVolumeClaim
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: mongodb-pvc
  namespace: mongodb
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 5Gi
  storageClassName: standard

---
# MongoDB Deployment
apiVersion: apps/v1
kind: Deployment
metadata:
  name: mongodb
  namespace: mongodb
  labels:
    app: mongodb
spec:
  replicas: 1
  selector:
    matchLabels:
      app: mongodb
  template:
    metadata:
      labels:
        app: mongodb
    spec:
      containers:
      - name: mongodb
        image: mongo:7.0
        ports:
        - containerPort: 27017
          name: mongodb
        env:
        - name: MONGO_INITDB_ROOT_USERNAME
          valueFrom:
            secretKeyRef:
              name: mongodb-secret
              key: mongodb-root-username
        - name: MONGO_INITDB_ROOT_PASSWORD
          valueFrom:
            secretKeyRef:
              name: mongodb-secret
              key: mongodb-root-password
        - name: MONGO_INITDB_DATABASE
          valueFrom:
            secretKeyRef:
              name: mongodb-secret
              key: mongodb-database
        volumeMounts:
        - name: mongodb-storage
          mountPath: /data/db
        resources:
          requests:
            memory: "256Mi"
            cpu: "125m"
          limits:
            memory: "512Mi"
            cpu: "250m"
        livenessProbe:
          exec:
            command:
            - mongosh
            - --eval
            - "db.adminCommand('ping')"
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        readinessProbe:
          exec:
            command:
            - mongosh
            - --eval
            - "db.adminCommand('ping')"
          initialDelaySeconds: 5
          periodSeconds: 5
          timeoutSeconds: 3
          failureThreshold: 3
      volumes:
      - name: mongodb-storage
        persistentVolumeClaim:
          claimName: mongodb-pvc

---
# MongoDB Service
apiVersion: v1
kind: Service
metadata:
  name: mongodb-service
  namespace: mongodb
  labels:
    app: mongodb
spec:
  type: ClusterIP
  selector:
    app: mongodb
  ports:
  - port: 27017
    targetPort: 27017
    name: mongodb

---
# MongoDB NodePort Service (for external access)
apiVersion: v1
kind: Service
metadata:
  name: mongodb-nodeport
  namespace: mongodb
  labels:
    app: mongodb
spec:
  type: NodePort
  selector:
    app: mongodb
  ports:
  - port: 27017
    targetPort: 27017
    nodePort: 30017
    name: mongodb
