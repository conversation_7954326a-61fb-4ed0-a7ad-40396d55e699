# MongoDB Kubernetes Deployment Guide

## Overview

I've created two MongoDB deployment configurations for Kubernetes:

1. **mongodb-k8s.yaml** - Production-ready replica set (3 instances)
2. **mongodb-simple-k8s.yaml** - Simple single-instance deployment

## Quick Start (Simple Deployment)

### 1. Deploy Simple MongoDB

```bash
# Apply the simple MongoDB deployment
kubectl apply -f mongodb-simple-k8s.yaml

# Check deployment status
kubectl get pods -n mongodb
kubectl get services -n mongodb
```

### 2. Access MongoDB

**Internal access (from within cluster):**
```
Connection String: mongodb://admin:<EMAIL>:27017/myapp
```

**External access (NodePort):**
```
Connection String: mongodb://admin:mongopass123@<NODE_IP>:30017/myapp
```

### 3. Test Connection

```bash
# Connect to MongoDB pod
kubectl exec -it deployment/mongodb -n mongodb -- mongosh

# Or connect with credentials
kubectl exec -it deployment/mongodb -n mongodb -- mongosh -u admin -p mongopass123
```

## Production Deployment (Replica Set)

### 1. Deploy Production MongoDB

```bash
# Apply the production MongoDB deployment
kubectl apply -f mongodb-k8s.yaml

# Wait for pods to be ready
kubectl get pods -n mongodb -w

# Initialize replica set (run the job)
kubectl get jobs -n mongodb
```

### 2. Verify Replica Set

```bash
# Connect to primary
kubectl exec -it mongodb-0 -n mongodb -- mongosh -u admin -p password123

# Check replica set status
rs.status()
```

## Configuration Details

### Simple Deployment Features

- **Single Instance**: 1 MongoDB pod
- **Storage**: 5Gi PersistentVolume
- **Resources**: 256Mi RAM, 125m CPU (requests)
- **Access**: ClusterIP + NodePort services
- **Credentials**: admin/mongopass123

### Production Deployment Features

- **Replica Set**: 3 MongoDB instances
- **High Availability**: Automatic failover
- **Storage**: 10Gi per instance
- **Resources**: 512Mi RAM, 250m CPU (requests)
- **Configuration**: Custom mongod.conf
- **Initialization**: Automated replica set setup

## Security Configuration

### Default Credentials (Change in Production!)

**Simple Deployment:**
```
Root Username: admin
Root Password: mongopass123
App Username: appuser
App Password: apppass
Database: myapp
```

**Production Deployment:**
```
Root Username: admin
Root Password: password123
App Username: mongodbuser
App Password: mongodbpass
Database: mongodb
```

### Update Credentials

1. **Encode new passwords:**
```bash
echo -n "newpassword" | base64
```

2. **Update Secret:**
```yaml
apiVersion: v1
kind: Secret
metadata:
  name: mongodb-secret
  namespace: mongodb
data:
  mongodb-root-password: <base64-encoded-password>
```

3. **Apply changes:**
```bash
kubectl apply -f mongodb-secret.yaml
kubectl rollout restart deployment/mongodb -n mongodb
```

## Storage Configuration

### Default Storage Class

Both deployments use `storageClassName: standard`. Update this based on your cluster:

```yaml
# For AWS EKS
storageClassName: gp2

# For GKE
storageClassName: standard-rwo

# For Azure AKS
storageClassName: default

# For local development
storageClassName: hostpath
```

### Custom Storage Size

Update the storage request in PVC:

```yaml
resources:
  requests:
    storage: 20Gi  # Change as needed
```

## Monitoring and Maintenance

### Check Pod Status

```bash
# List all MongoDB pods
kubectl get pods -n mongodb

# Describe pod for details
kubectl describe pod mongodb-0 -n mongodb

# Check logs
kubectl logs mongodb-0 -n mongodb
```

### Resource Usage

```bash
# Check resource consumption
kubectl top pods -n mongodb

# Check persistent volumes
kubectl get pv
kubectl get pvc -n mongodb
```

### Backup and Restore

```bash
# Create backup
kubectl exec -it mongodb-0 -n mongodb -- mongodump --host localhost --port 27017 -u admin -p password123 --out /tmp/backup

# Copy backup from pod
kubectl cp mongodb/mongodb-0:/tmp/backup ./mongodb-backup

# Restore from backup
kubectl cp ./mongodb-backup mongodb/mongodb-0:/tmp/restore
kubectl exec -it mongodb-0 -n mongodb -- mongorestore --host localhost --port 27017 -u admin -p password123 /tmp/restore
```

## Troubleshooting

### Common Issues

1. **Pods not starting:**
```bash
kubectl describe pod mongodb-0 -n mongodb
kubectl logs mongodb-0 -n mongodb
```

2. **Storage issues:**
```bash
kubectl get pvc -n mongodb
kubectl describe pvc mongodb-pvc -n mongodb
```

3. **Connection issues:**
```bash
# Test internal connectivity
kubectl run mongodb-client --rm -it --image mongo:7.0 -- mongosh mongodb://mongodb-service.mongodb.svc.cluster.local:27017
```

4. **Replica set issues:**
```bash
# Check replica set status
kubectl exec -it mongodb-0 -n mongodb -- mongosh -u admin -p password123 --eval "rs.status()"
```

### Performance Tuning

1. **Increase resources:**
```yaml
resources:
  requests:
    memory: "1Gi"
    cpu: "500m"
  limits:
    memory: "2Gi"
    cpu: "1000m"
```

2. **Optimize storage:**
```yaml
# Use SSD storage class
storageClassName: fast-ssd
```

3. **Configure MongoDB:**
```yaml
# Update mongod.conf in ConfigMap
data:
  mongod.conf: |
    storage:
      wiredTiger:
        engineConfig:
          cacheSizeGB: 1
```

## Cleanup

### Remove Simple Deployment

```bash
kubectl delete -f mongodb-simple-k8s.yaml
```

### Remove Production Deployment

```bash
kubectl delete -f mongodb-k8s.yaml
```

### Remove Persistent Data

```bash
# List PVs
kubectl get pv

# Delete specific PV (if needed)
kubectl delete pv mongodb-pv
```

## Integration Examples

### Connection from Application

```yaml
# Application deployment with MongoDB connection
env:
- name: MONGODB_URI
  value: "mongodb://admin:<EMAIL>:27017/myapp"
```

### Using ConfigMap for Connection String

```yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: app-config
data:
  mongodb-uri: "mongodb://mongodb-service.mongodb.svc.cluster.local:27017"
```

## Next Steps

1. **Choose deployment type** (simple vs production)
2. **Update credentials** for security
3. **Configure storage** based on your needs
4. **Set up monitoring** (optional)
5. **Test connectivity** from your applications

Would you like me to help you customize any of these configurations for your specific use case?
